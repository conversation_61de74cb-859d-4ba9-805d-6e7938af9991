#!/bin/bash

# AI Agent 集成测试运行脚本
# 这个脚本会运行真正调用DeepSeek大模型的集成测试

echo "=== AI Agent 集成测试 ==="
echo

# 检查API密钥
if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "❌ 错误: 请设置DEEPSEEK_API_KEY环境变量"
    echo "   export DEEPSEEK_API_KEY=your-actual-api-key"
    exit 1
fi

echo "✅ 检测到DEEPSEEK_API_KEY环境变量"
echo

# 选择要运行的测试
echo "请选择要运行的测试："
echo "1. 房源推荐内容生成 (testGeneratePropertyRecommendationContent)"
echo "2. 投资分析内容生成 (testGenerateInvestmentAnalysisContent)"
echo "3. 学区房分析内容生成 (testGenerateSchoolDistrictContent)"
echo "4. 运行所有集成测试"
echo

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🚀 运行房源推荐内容生成测试..."
        mvn test -Dtest=ContentGenerationIntegrationTest#testGeneratePropertyRecommendationContent -Dspring.profiles.active=integration
        ;;
    2)
        echo "🚀 运行投资分析内容生成测试..."
        mvn test -Dtest=ContentGenerationIntegrationTest#testGenerateInvestmentAnalysisContent -Dspring.profiles.active=integration
        ;;
    3)
        echo "🚀 运行学区房分析内容生成测试..."
        mvn test -Dtest=ContentGenerationIntegrationTest#testGenerateSchoolDistrictContent -Dspring.profiles.active=integration
        ;;
    4)
        echo "🚀 运行所有集成测试..."
        mvn test -Dtest=ContentGenerationIntegrationTest -Dspring.profiles.active=integration
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo
echo "=== 测试完成 ==="
echo
echo "💡 提示："
echo "- 如果测试失败，请检查网络连接和API密钥"
echo "- 生成的内容会在控制台输出"
echo "- 可以多次运行测试来比较不同的生成结果"
