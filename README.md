# AI Agent 房地产内容生成系统

基于DeepSeek大模型和SpringAI框架的房地产推广内容生成系统，通过Function Calling技术自动获取房源、市场、配套等数据，生成高质量的推广内容。

## 功能特性

- 🏠 **房源管理**: 完整的房源信息管理，包括基础信息、小区详情、交易需求等
- 🤖 **AI内容生成**: 使用DeepSeek大模型生成专业的房地产推广内容
- 📊 **Function Calling**: 通过函数调用自动获取房源数据、市场行情、周边配套等信息
- 🎯 **多平台适配**: 支持小红书、闲鱼、安居客等多个平台的内容格式
- 📈 **数据分析**: 提供价格对比、投资回报、市场趋势等分析功能
- 🎨 **图表生成**: 自动生成价格对比图、配套地图等可视化内容

## 技术栈

- **后端框架**: Spring Boot 3.4.0
- **AI框架**: Spring AI 1.0.0
- **大模型**: DeepSeek Chat API
- **构建工具**: Maven
- **测试框架**: JUnit 5 + MockMvc
- **Java版本**: JDK 17

## 快速开始

### 1. 环境准备

确保已安装以下软件：
- JDK 17+
- Maven 3.6+

### 2. 配置API密钥

在 `application.yml` 中配置DeepSeek API密钥：

```yaml
spring:
  ai:
    deepseek:
      api-key: ${DEEPSEEK_API_KEY:your-deepseek-api-key}
```

或者设置环境变量：
```bash
export DEEPSEEK_API_KEY=your-actual-api-key
```

### 3. 启动应用

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

应用将在 `http://localhost:8080` 启动。

## API 接口

### 房源管理

#### 获取所有房源
```http
GET /api/properties
```

#### 获取房源详情
```http
GET /api/properties/{propertyId}
```

#### 搜索房源
```http
GET /api/properties/search?communityName=星河湾&minPrice=500&maxPrice=1000
```

### 内容生成

#### 生成推广内容
```http
POST /api/content/generate
Content-Type: application/json

{
  "propertyId": "PROP_001",
  "contentType": "PROPERTY_RECOMMENDATION",
  "targetPlatforms": ["XIAOHONGSHU"],
  "targetAudience": "UPGRADING_BUYER",
  "contentStyle": "PROFESSIONAL",
  "wordCount": 600,
  "needCharts": true,
  "chartTypes": ["PRICE_COMPARISON", "AMENITIES_MAP"],
  "specialRequirements": "突出地段优势和投资价值"
}
```

#### 获取支持的配置选项
```http
GET /api/content/types          # 内容类型
GET /api/content/platforms      # 支持平台
GET /api/content/audiences      # 目标客户群体
GET /api/content/styles         # 内容风格
GET /api/content/chart-types    # 图表类型
```

## Tool Calling 功能

系统提供以下Tool供AI模型调用（使用Spring AI 1.0.0的@Tool注解）：

### 1. getPropertyData
获取房源详细信息
- 参数: propertyId (房源ID)

### 2. getMarketData
获取市场数据
- 参数: regionName (区域名称), dataType (数据类型，可选)

### 3. getSurroundingAmenities
获取周边配套信息
- 参数: propertyId (房源ID), amenityType (配套类型，可选), radius (查询半径，可选)

### 4. analyzePriceData
进行价格分析
- 参数: propertyId (房源ID), analysisType (分析类型，可选), comparisonRadius (对比半径，可选)

## 示例数据

系统内置了两套示例房源数据：

### 房源1: 星河湾 (PROP_001)
- 地址: 上海市徐汇区漕河泾开发区桂平路188号
- 户型: 3室2厅2卫，120㎡
- 价格: 830万 (69,167元/㎡)
- 特色: 满五唯一、南北通透、精装修、业主急售

### 房源2: 翠湖花园 (PROP_002)
- 地址: 上海市浦东新区张江高科技园区科苑路666号
- 户型: 2室1厅1卫，85㎡
- 价格: 560万 (65,882元/㎡)
- 特色: 地铁房、学区房、投资首选

## 生成内容示例

基于样例房源，系统可以生成类似以下格式的推广内容：

```
🏠【独家首发】内环稀缺次新房！单价回到10年前！满五唯一税费省20万🔥

📍 房源详情：
🏘️ 小区：星河湾（2010年建成）
🏠 户型：3室2厅2卫，建面120㎡，实用率85%
🧭 朝向：南北通透，主卧朝南，次卧朝北
💰 价格：830万（单价6.9万/㎡）

✨ 房源核心亮点：
• 满五唯一：免征增值税和个人所得税，省税费约30万
• 精装修：2019年重新装修，德系品牌，拎包入住
• 南北通透：空气对流好，冬暖夏凉
• 学区房：对口市重点小学，教育资源优质
• 业主诚售：因工作调动急售，价格可谈

📊 价格优势分析：
• 同小区同户型均价：950万
• 本套房源报价：830万
• 价格优势：低于市场价120万（12.6%）

🚇 交通便利：
• 地铁12号线桂林路站500米，步行5分钟
• 地铁9号线漕河泾开发区站800米，步行8分钟
• 中环高架入口1km，自驾无忧

#上海买房攻略 #内环捡漏 #次新房 #满五唯一 #房产干货
```

## 测试

运行所有测试：
```bash
mvn test
```

运行特定测试类：
```bash
mvn test -Dtest=PropertyServiceTest
mvn test -Dtest=ContentGenerationControllerTest
```

## 项目结构

```
src/
├── main/
│   ├── java/com/dcai/aiagent/
│   │   ├── controller/          # REST API控制器
│   │   ├── service/             # 业务逻辑服务
│   │   ├── model/               # 数据模型
│   │   ├── function/            # Function Calling实现
│   │   └── config/              # 配置类
│   └── resources/
│       └── application.yml      # 应用配置
└── test/
    └── java/com/dcai/aiagent/   # 测试用例
```

## 扩展开发

### 添加新的Function

1. 在 `function` 包下创建新的Function类
2. 实现 `Function<Request, Response>` 接口
3. 在 `AiConfig` 中注册Function
4. 编写相应的测试用例

### 添加新的数据源

1. 在 `service` 包下创建新的Service类
2. 在相应的Function中调用新的Service
3. 更新模拟数据或连接真实数据源

## 注意事项

1. **API密钥安全**: 请妥善保管DeepSeek API密钥，不要提交到代码仓库
2. **数据模拟**: 当前版本使用模拟数据，生产环境需要连接真实数据源
3. **内容合规**: 生成的内容需要符合各平台的发布规范
4. **性能优化**: 大量请求时建议实现缓存和限流机制

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。
