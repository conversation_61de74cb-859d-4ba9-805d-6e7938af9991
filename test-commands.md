# 集成测试运行命令

## 前提条件
确保已设置DeepSeek API密钥：
```bash
export DEEPSEEK_API_KEY=your-actual-deepseek-api-key
```

## 运行单个测试方法

### 1. 房源推荐内容生成测试（推荐）
这个测试会调用所有Function Calling并生成完整推文：
```bash
mvn test -Dtest=ContentGenerationIntegrationTest#testGeneratePropertyRecommendationContent
```

### 2. 投资分析内容生成测试
```bash
mvn test -Dtest=ContentGenerationIntegrationTest#testGenerateInvestmentAnalysisContent
```

### 3. 学区房分析内容生成测试
```bash
mvn test -Dtest=ContentGenerationIntegrationTest#testGenerateSchoolDistrictContent
```

## 运行所有集成测试
```bash
mvn test -Dtest=ContentGenerationIntegrationTest
```

## 运行时会发生什么

1. **启动Spring Boot应用上下文**
2. **加载模拟房源数据**（星河湾、翠湖花园）
3. **调用DeepSeek大模型**
4. **AI模型会自动调用以下Function：**
   - `getPropertyData("PROP_001")` - 获取星河湾房源详情
   - `getMarketData("徐汇区", "all")` - 获取徐汇区市场数据
   - `getSurroundingAmenities("PROP_001", "all", 2000)` - 获取周边配套
   - `analyzePriceData("PROP_001", "all", 1000)` - 进行价格分析
5. **基于真实数据生成推文内容**
6. **在控制台输出完整的推文内容**

## 预期输出示例

测试成功后，您会看到类似以下的输出：

```
=== 生成的推文内容 ===
标题: 🏠【独家首发】内环稀缺次新房！单价回到10年前！满五唯一税费省20万🔥
字数: 580
标签: [房产推荐, 买房攻略, 投资置业, 满五唯一, 内环捡漏]
正文:
📍 房源详情：
🏘️ 小区：星河湾（2010年建成）
🏠 户型：3室2厅2卫，建面120㎡，实用率85%
🧭 朝向：南北通透，主卧朝南，次卧朝北
💰 价格：830万（单价6.9万/㎡）

✨ 房源核心亮点：
• 满五唯一：免征增值税和个人所得税，省税费约30万
• 精装修：2019年重新装修，德系品牌，拎包入住
• 南北通透：空气对流好，冬暖夏凉
• 学区房：对口市重点小学，教育资源优质
• 业主诚售：因工作调动急售，价格可谈

📊 价格优势分析：
• 同小区同户型均价：950万
• 本套房源报价：830万
• 价格优势：低于市场价120万（12.6%）

🚇 交通便利：
• 地铁12号线桂林路站500米，步行5分钟
• 地铁9号线漕河泾开发区站800米，步行8分钟
• 中环高架入口1km，自驾无忧

#上海买房攻略 #内环捡漏 #次新房 #满五唯一 #房产干货
=== 内容生成完成 ===
```

## 故障排除

如果测试失败，请检查：
1. ✅ DEEPSEEK_API_KEY环境变量是否正确设置
2. ✅ 网络连接是否正常
3. ✅ API密钥是否有效且有足够余额
4. ✅ 防火墙是否阻止了API调用

## 注意事项

- 每次运行都会产生API调用费用
- 生成的内容可能每次都不同（由于AI的随机性）
- 测试运行时间约30-60秒（取决于网络和API响应速度）
- 建议先运行第一个测试方法验证功能正常
