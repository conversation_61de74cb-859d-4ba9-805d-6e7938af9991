spring:
  application:
    name: aiagent
  ai:
    deepseek:
      # 配置DeepSeek API
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.7
          max-tokens: 2000
          top-p: 0.9

server:
  port: 8080
  servlet:
    context-path: /api

logging:
  level:
    com.dcai.aiagent: DEBUG
    org.springframework.ai: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 应用配置
app:
  content:
    # 推文生成配置
    generation:
      default-word-count: 600
      max-word-count: 800
      min-word-count: 500
    # 支持的平台
    platforms:
      - xiaohongshu
      - xianyu
      - anjuke
    # 推文类型
    types:
      - property-recommendation
      - market-analysis
      - lifestyle-amenities
      - buying-guide
