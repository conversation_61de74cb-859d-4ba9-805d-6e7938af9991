package com.dcai.aiagent.function;

import com.dcai.aiagent.model.MarketData;
import com.dcai.aiagent.service.MarketDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

/**
 * 市场数据查询工具类
 * 用于AI模型通过Tool Calling获取市场行情、成交数据等信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MarketDataFunction {

    private final MarketDataService marketDataService;

    @Tool(description = "获取区域市场数据，包括挂牌信息、成交数据、市场趋势、政策信息等")
    public MarketData getMarketData(
            @ToolParam(description = "区域名称，如：浦东新区、徐汇区等") String regionName,
            @ToolParam(description = "数据类型：listing(挂牌数据)、transaction(成交数据)、trend(市场趋势)、policy(政策信息)、all(全部数据)", required = false) String dataType) {

        log.info("获取市场数据，区域: {}, 类型: {}", regionName, dataType);

        try {
            MarketData marketData = marketDataService.getMarketData(regionName, dataType);
            if (marketData == null) {
                throw new RuntimeException("该区域暂无市场数据: " + regionName);
            }

            log.info("成功获取市场数据: {}", regionName);
            return marketData;
        } catch (Exception e) {
            log.error("获取市场数据失败，区域: {}", regionName, e);
            throw new RuntimeException("获取市场数据失败: " + e.getMessage());
        }
    }
}
