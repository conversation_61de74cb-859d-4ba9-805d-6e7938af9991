package com.dcai.aiagent.function;

import com.dcai.aiagent.model.Property;
import com.dcai.aiagent.service.PropertyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

/**
 * 房源数据查询工具类
 * 用于AI模型通过Tool Calling获取房源详细信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PropertyDataFunction {

    private final PropertyService propertyService;

    @Tool(description = "获取房源详细信息，包括基础信息、小区详情、交易需求等")
    public Property getPropertyData(@ToolParam(description = "房源ID") String propertyId) {
        log.info("获取房源数据，房源ID: {}", propertyId);

        try {
            Property property = propertyService.getPropertyById(propertyId);
            if (property == null) {
                throw new RuntimeException("房源不存在: " + propertyId);
            }

            log.info("成功获取房源数据: {}", property.getCommunityName());
            return property;
        } catch (Exception e) {
            log.error("获取房源数据失败，房源ID: {}", propertyId, e);
            throw new RuntimeException("获取房源数据失败: " + e.getMessage());
        }
    }
}
