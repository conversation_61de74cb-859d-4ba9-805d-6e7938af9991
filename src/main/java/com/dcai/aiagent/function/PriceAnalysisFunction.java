package com.dcai.aiagent.function;

import com.dcai.aiagent.service.PriceAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格分析工具类
 * 用于AI模型通过Tool Calling获取价格对比、投资回报等分析数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PriceAnalysisFunction {

    private final PriceAnalysisService priceAnalysisService;

    @Tool(description = "进行房源价格分析，包括同区域对比、投资回报率计算、价格趋势分析等")
    public PriceAnalysisResult analyzePriceData(
            @ToolParam(description = "房源ID") String propertyId,
            @ToolParam(description = "分析类型：comparison(价格对比)、investment(投资回报)、trend(价格趋势)、all(全部分析)", required = false) String analysisType,
            @ToolParam(description = "对比半径，单位：米，默认1000米", required = false) Integer comparisonRadius) {

        log.info("进行价格分析，房源ID: {}, 分析类型: {}", propertyId, analysisType);

        try {
            PriceAnalysisResult result = priceAnalysisService.analyzePriceData(
                propertyId,
                analysisType,
                comparisonRadius
            );

            log.info("价格分析完成: {}", propertyId);
            return result;
        } catch (Exception e) {
            log.error("价格分析失败，房源ID: {}", propertyId, e);
            throw new RuntimeException("价格分析失败: " + e.getMessage());
        }
    }

    /**
     * 价格分析结果
     */
    public record PriceAnalysisResult(
            String propertyId,
            BigDecimal currentPrice,
            BigDecimal unitPrice,
            MarketComparison marketComparison,
            InvestmentAnalysis investmentAnalysis,
            String priceAdvantage,
            List<String> recommendations
    ) {}

    /**
     * 市场对比数据
     */
    public record MarketComparison(
            BigDecimal averagePrice,
            String priceRange,
            Integer pricePercentile,
            List<SimilarProperty> similarProperties
    ) {}

    /**
     * 相似房源对比
     */
    public record SimilarProperty(
            String communityName,
            BigDecimal price,
            BigDecimal unitPrice,
            BigDecimal area,
            String layout
    ) {}

    /**
     * 投资分析数据
     */
    public record InvestmentAnalysis(
            BigDecimal expectedRentalYield,
            BigDecimal monthlyRent,
            String appreciationPotential,
            Integer paybackPeriod,
            String riskLevel
    ) {}
}
