package com.dcai.aiagent.function;

import com.dcai.aiagent.model.SurroundingAmenities;
import com.dcai.aiagent.service.SurroundingAmenitiesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

/**
 * 周边配套信息查询工具类
 * 用于AI模型通过Tool Calling获取学区、交通、生活配套等信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SurroundingAmenitiesFunction {

    private final SurroundingAmenitiesService amenitiesService;

    @Tool(description = "获取房源周边配套信息，包括学区、交通、生活配套、产业环境、规划潜力等")
    public SurroundingAmenities getSurroundingAmenities(
            @ToolParam(description = "房源ID") String propertyId,
            @ToolParam(description = "配套类型：education(学区)、transportation(交通)、life(生活配套)、industry(产业环境)、development(规划潜力)、all(全部配套)", required = false) String amenityType,
            @ToolParam(description = "查询半径，单位：米，默认2000米", required = false) Integer radius) {

        log.info("获取周边配套信息，房源ID: {}, 配套类型: {}", propertyId, amenityType);

        try {
            SurroundingAmenities amenities = amenitiesService.getSurroundingAmenities(
                propertyId,
                amenityType,
                radius
            );

            if (amenities == null) {
                throw new RuntimeException("该房源暂无周边配套信息: " + propertyId);
            }

            log.info("成功获取周边配套信息: {}", propertyId);
            return amenities;
        } catch (Exception e) {
            log.error("获取周边配套信息失败，房源ID: {}", propertyId, e);
            throw new RuntimeException("获取周边配套信息失败: " + e.getMessage());
        }
    }
}
