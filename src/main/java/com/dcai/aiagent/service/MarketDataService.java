package com.dcai.aiagent.service;

import com.dcai.aiagent.model.MarketData;
import com.dcai.aiagent.model.Property;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 市场数据服务
 * 提供市场行情、成交数据、政策信息等
 */
@Slf4j
@Service
public class MarketDataService {

    // 模拟市场数据存储
    private final Map<String, MarketData> marketDataDatabase = new HashMap<>();

    public MarketDataService() {
        initializeMockData();
    }

    /**
     * 获取区域市场数据
     */
    public MarketData getMarketData(String regionName, String dataType) {
        log.info("查询市场数据，区域: {}, 类型: {}", regionName, dataType);
        return marketDataDatabase.get(regionName);
    }

    /**
     * 初始化模拟市场数据
     */
    private void initializeMockData() {
        // 徐汇区市场数据
        MarketData xuhuiData = MarketData.builder()
                .regionName("徐汇区")
                .listingData(MarketData.ListingData.builder()
                        .totalListings(2580)
                        .averagePrice(new BigDecimal("95000"))
                        .priceRange("6-15万/平")
                        .newListingsThisMonth(156)
                        .build())
                .transactionData(MarketData.TransactionData.builder()
                        .recentTransactions(Arrays.asList(
                                MarketData.RecentTransaction.builder()
                                        .communityName("星河湾")
                                        .layout("3室2厅")
                                        .area(new BigDecimal("120"))
                                        .floor("6/18层")
                                        .orientation("南北通透")
                                        .transactionPrice(new BigDecimal("850"))
                                        .transactionDate(LocalDate.now().minusDays(5))
                                        .daysOnMarket(45)
                                        .build(),
                                MarketData.RecentTransaction.builder()
                                        .communityName("金茂府")
                                        .layout("4室2厅")
                                        .area(new BigDecimal("140"))
                                        .floor("12/25层")
                                        .orientation("南向")
                                        .transactionPrice(new BigDecimal("1200"))
                                        .transactionDate(LocalDate.now().minusDays(12))
                                        .daysOnMarket(38)
                                        .build()
                        ))
                        .averageTransactionPrice(new BigDecimal("92000"))
                        .transactionVolume(186)
                        .averageDaysOnMarket(42)
                        .build())
                .marketTrend(MarketData.MarketTrend.builder()
                        .priceChangeRate(new BigDecimal("3.2"))
                        .trendDirection("上涨")
                        .inventoryLevel(2580)
                        .demandSupplyRatio(new BigDecimal("1.2"))
                        .marketHeat("热度较高")
                        .build())
                .policy(MarketData.PropertyPolicy.builder()
                        .purchaseRestriction("限购2套")
                        .loanRestriction("首套30%，二套40%")
                        .taxPolicy(MarketData.TaxPolicy.builder()
                                .vatRate(new BigDecimal("5.6"))
                                .deedTaxRate(new BigDecimal("3.0"))
                                .personalIncomeTaxRate(new BigDecimal("20"))
                                .taxExemptionCondition("满五唯一免征增值税和个税")
                                .build())
                        .housingFundPolicy("最高贷款120万")
                        .talentPolicy("人才可享受购房补贴")
                        .build())
                .financialInfo(MarketData.FinancialInfo.builder()
                        .currentLPR(new BigDecimal("3.45"))
                        .firstHomeLoanRate(new BigDecimal("3.1"))
                        .secondHomeLoanRate(new BigDecimal("3.6"))
                        .firstHomeDownPayment(new BigDecimal("30"))
                        .secondHomeDownPayment(new BigDecimal("40"))
                        .loanApprovalCycle("15-20个工作日")
                        .build())
                .build();

        // 浦东新区市场数据
        MarketData pudongData = MarketData.builder()
                .regionName("浦东新区")
                .listingData(MarketData.ListingData.builder()
                        .totalListings(4200)
                        .averagePrice(new BigDecimal("68000"))
                        .priceRange("4-12万/平")
                        .newListingsThisMonth(280)
                        .build())
                .transactionData(MarketData.TransactionData.builder()
                        .recentTransactions(Arrays.asList(
                                MarketData.RecentTransaction.builder()
                                        .communityName("翠湖花园")
                                        .layout("2室1厅")
                                        .area(new BigDecimal("85"))
                                        .floor("8/20层")
                                        .orientation("南向")
                                        .transactionPrice(new BigDecimal("580"))
                                        .transactionDate(LocalDate.now().minusDays(3))
                                        .daysOnMarket(28)
                                        .build(),
                                MarketData.RecentTransaction.builder()
                                        .communityName("张江汤臣豪园")
                                        .layout("3室2厅")
                                        .area(new BigDecimal("110"))
                                        .floor("15/30层")
                                        .orientation("南北通透")
                                        .transactionPrice(new BigDecimal("880"))
                                        .transactionDate(LocalDate.now().minusDays(8))
                                        .daysOnMarket(35)
                                        .build()
                        ))
                        .averageTransactionPrice(new BigDecimal("65000"))
                        .transactionVolume(320)
                        .averageDaysOnMarket(32)
                        .build())
                .marketTrend(MarketData.MarketTrend.builder()
                        .priceChangeRate(new BigDecimal("5.8"))
                        .trendDirection("上涨")
                        .inventoryLevel(4200)
                        .demandSupplyRatio(new BigDecimal("1.5"))
                        .marketHeat("热度很高")
                        .build())
                .policy(MarketData.PropertyPolicy.builder()
                        .purchaseRestriction("限购2套")
                        .loanRestriction("首套30%，二套40%")
                        .taxPolicy(MarketData.TaxPolicy.builder()
                                .vatRate(new BigDecimal("5.6"))
                                .deedTaxRate(new BigDecimal("3.0"))
                                .personalIncomeTaxRate(new BigDecimal("20"))
                                .taxExemptionCondition("满五唯一免征增值税和个税")
                                .build())
                        .housingFundPolicy("最高贷款120万")
                        .talentPolicy("张江人才可享受优惠政策")
                        .build())
                .financialInfo(MarketData.FinancialInfo.builder()
                        .currentLPR(new BigDecimal("3.45"))
                        .firstHomeLoanRate(new BigDecimal("3.1"))
                        .secondHomeLoanRate(new BigDecimal("3.6"))
                        .firstHomeDownPayment(new BigDecimal("30"))
                        .secondHomeDownPayment(new BigDecimal("40"))
                        .loanApprovalCycle("15-20个工作日")
                        .build())
                .build();

        marketDataDatabase.put("徐汇区", xuhuiData);
        marketDataDatabase.put("浦东新区", pudongData);
        
        log.info("初始化市场数据完成，覆盖{}个区域", marketDataDatabase.size());
    }
}
