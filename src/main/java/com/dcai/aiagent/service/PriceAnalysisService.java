package com.dcai.aiagent.service;

import com.dcai.aiagent.function.PriceAnalysisFunction.PriceAnalysisResult;
import com.dcai.aiagent.function.PriceAnalysisFunction.MarketComparison;
import com.dcai.aiagent.function.PriceAnalysisFunction.InvestmentAnalysis;
import com.dcai.aiagent.function.PriceAnalysisFunction.SimilarProperty;
import com.dcai.aiagent.model.Property;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

/**
 * 价格分析服务
 * 提供价格对比、投资回报分析等功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PriceAnalysisService {

    private final PropertyService propertyService;

    /**
     * 分析房源价格数据
     */
    public PriceAnalysisResult analyzePriceData(String propertyId, String analysisType, Integer comparisonRadius) {
        log.info("分析房源价格，ID: {}, 类型: {}, 半径: {}米", propertyId, analysisType, comparisonRadius);
        
        Property property = propertyService.getPropertyById(propertyId);
        if (property == null) {
            throw new RuntimeException("房源不存在");
        }

        // 构建价格分析结果
        return new PriceAnalysisResult(
                propertyId,
                property.getListPrice(),
                property.getUnitPrice(),
                buildMarketComparison(property),
                buildInvestmentAnalysis(property),
                analyzePriceAdvantage(property),
                generateRecommendations(property)
        );
    }

    /**
     * 构建市场对比数据
     */
    private MarketComparison buildMarketComparison(Property property) {
        // 模拟区域平均价格数据
        BigDecimal averagePrice = property.getUnitPrice().multiply(new BigDecimal("1.1"));
        
        // 计算价格分位数
        int pricePercentile = calculatePricePercentile(property.getUnitPrice(), averagePrice);
        
        // 构建相似房源对比
        List<SimilarProperty> similarProperties = buildSimilarProperties(property);
        
        return new MarketComparison(
                averagePrice.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP),
                generatePriceRange(averagePrice),
                pricePercentile,
                similarProperties
        );
    }

    /**
     * 构建投资分析数据
     */
    private InvestmentAnalysis buildInvestmentAnalysis(Property property) {
        // 计算预期租金收益率
        BigDecimal monthlyRent = calculateMonthlyRent(property);
        BigDecimal yearlyRent = monthlyRent.multiply(new BigDecimal("12"));
        BigDecimal totalPrice = property.getListPrice().multiply(new BigDecimal("10000"));
        BigDecimal rentalYield = yearlyRent.divide(totalPrice, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
        
        return new InvestmentAnalysis(
                rentalYield,
                monthlyRent,
                evaluateAppreciationPotential(property),
                calculatePaybackPeriod(rentalYield),
                assessRiskLevel(property)
        );
    }

    /**
     * 分析价格优势
     */
    private String analyzePriceAdvantage(Property property) {
        StringBuilder advantage = new StringBuilder();
        
        // 基于房源特色分析价格优势
        if (property.getFeatures().contains("满五唯一")) {
            advantage.append("满五唯一，免征增值税和个税，节省税费约20-30万元；");
        }
        
        if (property.getFeatures().contains("南北通透")) {
            advantage.append("南北通透户型，采光通风佳，居住舒适度高；");
        }
        
        if (property.getFeatures().contains("地铁房")) {
            advantage.append("地铁房，交通便利，保值增值能力强；");
        }
        
        if (property.getFeatures().contains("学区房")) {
            advantage.append("学区房，教育资源优质，需求稳定；");
        }
        
        // 基于价格分析
        advantage.append("当前挂牌价格合理，具有一定议价空间。");
        
        return advantage.toString();
    }

    /**
     * 生成购买建议
     */
    private List<String> generateRecommendations(Property property) {
        return Arrays.asList(
                "建议关注房源的地段价值和配套设施",
                "可适当议价，预留5-10%的议价空间",
                "注意核实房源的产权状况和税费情况",
                "建议实地看房，了解房源实际状况",
                "考虑自身资金状况和还款能力"
        );
    }

    /**
     * 计算价格分位数
     */
    private int calculatePricePercentile(BigDecimal unitPrice, BigDecimal averagePrice) {
        if (unitPrice.compareTo(averagePrice) < 0) {
            return 35; // 低于平均价格
        } else if (unitPrice.compareTo(averagePrice.multiply(new BigDecimal("1.2"))) < 0) {
            return 65; // 略高于平均价格
        } else {
            return 85; // 明显高于平均价格
        }
    }

    /**
     * 构建相似房源对比
     */
    private List<SimilarProperty> buildSimilarProperties(Property property) {
        // 模拟相似房源数据
        return Arrays.asList(
                new SimilarProperty(
                        "金茂府",
                        property.getListPrice().multiply(new BigDecimal("1.2")),
                        property.getUnitPrice().multiply(new BigDecimal("1.15")),
                        property.getArea().add(new BigDecimal("10")),
                        property.getLayout().getLayoutDescription()
                ),
                new SimilarProperty(
                        "绿地中央广场",
                        property.getListPrice().multiply(new BigDecimal("0.9")),
                        property.getUnitPrice().multiply(new BigDecimal("0.95")),
                        property.getArea().subtract(new BigDecimal("5")),
                        property.getLayout().getLayoutDescription()
                )
        );
    }

    /**
     * 计算月租金
     */
    private BigDecimal calculateMonthlyRent(Property property) {
        // 基于面积和单价估算租金
        BigDecimal baseRent = property.getArea().multiply(new BigDecimal("80")); // 80元/平/月
        
        // 根据房源特色调整
        if (property.getFeatures().contains("精装修")) {
            baseRent = baseRent.multiply(new BigDecimal("1.2"));
        }
        if (property.getFeatures().contains("地铁房")) {
            baseRent = baseRent.multiply(new BigDecimal("1.1"));
        }
        
        return baseRent;
    }

    /**
     * 生成价格区间
     */
    private String generatePriceRange(BigDecimal averagePrice) {
        BigDecimal minPrice = averagePrice.multiply(new BigDecimal("0.7"));
        BigDecimal maxPrice = averagePrice.multiply(new BigDecimal("1.5"));
        
        return String.format("%.1f-%.1f万/平", 
                minPrice.divide(new BigDecimal("10000"), 1, RoundingMode.HALF_UP).doubleValue(),
                maxPrice.divide(new BigDecimal("10000"), 1, RoundingMode.HALF_UP).doubleValue());
    }

    /**
     * 评估增值潜力
     */
    private String evaluateAppreciationPotential(Property property) {
        if (property.getFeatures().contains("地铁房") && property.getFeatures().contains("学区房")) {
            return "高";
        } else if (property.getFeatures().contains("地铁房") || property.getFeatures().contains("学区房")) {
            return "中等";
        } else {
            return "一般";
        }
    }

    /**
     * 计算投资回收期
     */
    private Integer calculatePaybackPeriod(BigDecimal rentalYield) {
        if (rentalYield.compareTo(new BigDecimal("4")) >= 0) {
            return 20;
        } else if (rentalYield.compareTo(new BigDecimal("3")) >= 0) {
            return 25;
        } else {
            return 30;
        }
    }

    /**
     * 评估投资风险等级
     */
    private String assessRiskLevel(Property property) {
        if (property.getFeatures().contains("地铁房") && property.getBuildYear() > 2010) {
            return "低风险";
        } else if (property.getBuildYear() > 2000) {
            return "中等风险";
        } else {
            return "较高风险";
        }
    }
}
