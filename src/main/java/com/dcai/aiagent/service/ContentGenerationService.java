package com.dcai.aiagent.service;

import com.dcai.aiagent.function.MarketDataFunction;
import com.dcai.aiagent.function.PriceAnalysisFunction;
import com.dcai.aiagent.function.PropertyDataFunction;
import com.dcai.aiagent.function.SurroundingAmenitiesFunction;
import com.dcai.aiagent.model.ContentGenerationRequest;
import com.dcai.aiagent.model.ContentGenerationResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 内容生成服务
 * 使用AI模型和Function Calling生成房地产推广内容
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContentGenerationService {

    private final ChatClient chatClient;
    private final PropertyDataFunction propertyDataFunction;
    private final MarketDataFunction marketDataFunction;
    private final SurroundingAmenitiesFunction surroundingAmenitiesFunction;
    private final PriceAnalysisFunction priceAnalysisFunction;

    /**
     * 生成推广内容
     */
    public ContentGenerationResponse generateContent(ContentGenerationRequest request) {
        log.info("开始生成内容，房源ID: {}, 类型: {}", request.getPropertyId(), request.getContentType());
        
        long startTime = System.currentTimeMillis();
        String taskId = UUID.randomUUID().toString();
        
        try {
            // 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);
            
            // 构建用户提示词
            String userPrompt = buildUserPrompt(request);
            
            // 调用AI模型生成内容
            String responseContent = chatClient.prompt()
                    .system(systemPrompt)
                    .user(userPrompt)
                    .call()
                    .content();

            // 解析生成的内容
            ContentGenerationResponse.GeneratedContent content = parseGeneratedContent(
                    responseContent, request);
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            return ContentGenerationResponse.builder()
                    .taskId(taskId)
                    .propertyId(request.getPropertyId())
                    .content(content)
                    .generatedAt(LocalDateTime.now())
                    .processingTime(processingTime)
                    .status(ContentGenerationResponse.GenerationStatus.SUCCESS)
                    .build();
                    
        } catch (Exception e) {
            log.error("内容生成失败，房源ID: {}", request.getPropertyId(), e);
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            return ContentGenerationResponse.builder()
                    .taskId(taskId)
                    .propertyId(request.getPropertyId())
                    .generatedAt(LocalDateTime.now())
                    .processingTime(processingTime)
                    .status(ContentGenerationResponse.GenerationStatus.FAILED)
                    .errorMessage(e.getMessage())
                    .build();
        }
    }

    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(ContentGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一位专业的房地产经纪人和内容营销专家，擅长创作吸引人的房产推广内容。\n\n");
        
        prompt.append("你的任务是根据提供的房源信息，生成").append(request.getContentType().getDescription()).append("类型的推广内容。\n\n");
        
        prompt.append("内容要求：\n");
        prompt.append("1. 字数控制在").append(request.getWordCount() != null ? request.getWordCount() : 600).append("字左右\n");
        prompt.append("2. 风格：").append(request.getContentStyle() != null ? request.getContentStyle().getDescription() : "专业理性").append("\n");
        prompt.append("3. 目标客户：").append(request.getTargetAudience() != null ? request.getTargetAudience().getDescription() : "改善型购房").append("\n");
        prompt.append("4. 发布平台：").append(request.getTargetPlatforms().stream()
                .map(p -> p.getDescription()).reduce((a, b) -> a + "、" + b).orElse("小红书")).append("\n\n");
        
        prompt.append("你可以调用以下函数获取详细信息：\n");
        prompt.append("- getPropertyData: 获取房源详细信息\n");
        prompt.append("- getMarketData: 获取市场数据\n");
        prompt.append("- getSurroundingAmenities: 获取周边配套信息\n");
        prompt.append("- analyzePriceData: 获取价格分析数据\n\n");
        
        prompt.append("请按照以下结构生成内容：\n");
        prompt.append("1. 吸引人的标题（包含核心卖点）\n");
        prompt.append("2. 导语（制造紧迫感和吸引力）\n");
        prompt.append("3. 房源亮点（突出核心优势）\n");
        prompt.append("4. 数据支撑（价格对比、市场分析）\n");
        prompt.append("5. 配套介绍（交通、学区、生活配套）\n");
        prompt.append("6. 投资价值分析\n");
        prompt.append("7. 行动召唤（联系方式、看房预约）\n");
        prompt.append("8. 相关标签\n\n");
        
        prompt.append("注意事项：\n");
        prompt.append("- 使用emoji和符号增强视觉效果\n");
        prompt.append("- 数据要准确，来源要标注\n");
        prompt.append("- 避免夸大宣传，保持真实性\n");
        prompt.append("- 符合各平台的内容规范\n");
        
        return prompt.toString();
    }

    /**
     * 构建用户提示词
     */
    private String buildUserPrompt(ContentGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请为房源ID: ").append(request.getPropertyId()).append(" 生成").append(request.getContentType().getDescription()).append("内容。\n\n");
        
        if (request.getSpecialRequirements() != null && !request.getSpecialRequirements().trim().isEmpty()) {
            prompt.append("特殊要求：").append(request.getSpecialRequirements()).append("\n\n");
        }
        
        prompt.append("请先调用相关函数获取房源信息、市场数据、周边配套和价格分析，然后基于这些真实数据生成高质量的推广内容。");
        
        return prompt.toString();
    }

    /**
     * 解析生成的内容
     */
    private ContentGenerationResponse.GeneratedContent parseGeneratedContent(String aiResponse, ContentGenerationRequest request) {
        // 简单的内容解析，实际项目中可能需要更复杂的解析逻辑
        String[] lines = aiResponse.split("\n");
        
        String title = extractTitle(lines);
        String body = aiResponse;
        List<String> tags = extractTags(aiResponse);
        int wordCount = countWords(aiResponse);
        
        // 生成图表数据（如果需要）
        List<ContentGenerationResponse.ChartData> charts = new ArrayList<>();
        if (Boolean.TRUE.equals(request.getNeedCharts()) && request.getChartTypes() != null) {
            charts = generateChartData(request.getChartTypes());
        }
        
        // 生成平台适配内容
        Map<String, ContentGenerationResponse.PlatformContent> platformContents = generatePlatformContents(
                title, body, tags, request.getTargetPlatforms());
        
        // 评估内容质量
        ContentGenerationResponse.ContentQuality quality = evaluateContentQuality(aiResponse, request);
        
        return ContentGenerationResponse.GeneratedContent.builder()
                .title(title)
                .body(body)
                .tags(tags)
                .wordCount(wordCount)
                .charts(charts)
                .platformContents(platformContents)
                .quality(quality)
                .build();
    }

    /**
     * 提取标题
     */
    private String extractTitle(String[] lines) {
        for (String line : lines) {
            if (line.startsWith("**") && line.endsWith("**") && line.length() > 4) {
                return line.substring(2, line.length() - 2);
            }
            if (line.startsWith("# ")) {
                return line.substring(2);
            }
        }
        return "精品房源推荐";
    }

    /**
     * 提取标签
     */
    private List<String> extractTags(String content) {
        List<String> tags = new ArrayList<>();
        
        // 查找#标签
        String[] words = content.split("\\s+");
        for (String word : words) {
            if (word.startsWith("#") && word.length() > 1) {
                tags.add(word.substring(1));
            }
        }
        
        // 如果没有找到标签，添加默认标签
        if (tags.isEmpty()) {
            tags.addAll(Arrays.asList("房产推荐", "买房攻略", "投资置业"));
        }
        
        return tags;
    }

    /**
     * 统计字数
     */
    private int countWords(String content) {
        return content.replaceAll("\\s+", "").length();
    }

    /**
     * 生成图表数据
     */
    private List<ContentGenerationResponse.ChartData> generateChartData(List<ContentGenerationRequest.ChartType> chartTypes) {
        List<ContentGenerationResponse.ChartData> charts = new ArrayList<>();
        
        for (ContentGenerationRequest.ChartType chartType : chartTypes) {
            ContentGenerationResponse.ChartData chart = ContentGenerationResponse.ChartData.builder()
                    .type(chartType.name())
                    .title(chartType.getDescription())
                    .data(generateMockChartData(chartType))
                    .config(generateChartConfig(chartType))
                    .description("基于真实市场数据生成的" + chartType.getDescription())
                    .build();
            charts.add(chart);
        }
        
        return charts;
    }

    /**
     * 生成模拟图表数据
     */
    private Object generateMockChartData(ContentGenerationRequest.ChartType chartType) {
        Map<String, Object> data = new HashMap<>();
        
        switch (chartType) {
            case PRICE_COMPARISON:
                data.put("labels", Arrays.asList("本房源", "同小区均价", "区域均价"));
                data.put("values", Arrays.asList(830, 950, 920));
                break;
            case MARKET_TREND:
                data.put("months", Arrays.asList("1月", "2月", "3月", "4月", "5月", "6月"));
                data.put("prices", Arrays.asList(85000, 87000, 89000, 91000, 93000, 95000));
                break;
            default:
                data.put("message", "图表数据生成中...");
        }
        
        return data;
    }

    /**
     * 生成图表配置
     */
    private Map<String, Object> generateChartConfig(ContentGenerationRequest.ChartType chartType) {
        Map<String, Object> config = new HashMap<>();
        config.put("type", "bar");
        config.put("responsive", true);
        config.put("theme", "light");
        return config;
    }

    /**
     * 生成平台适配内容
     */
    private Map<String, ContentGenerationResponse.PlatformContent> generatePlatformContents(
            String title, String body, List<String> tags, List<ContentGenerationRequest.Platform> platforms) {
        
        Map<String, ContentGenerationResponse.PlatformContent> platformContents = new HashMap<>();
        
        for (ContentGenerationRequest.Platform platform : platforms) {
            ContentGenerationResponse.PlatformContent platformContent = ContentGenerationResponse.PlatformContent.builder()
                    .platform(platform.getDescription())
                    .adaptedTitle(adaptTitleForPlatform(title, platform))
                    .adaptedContent(adaptContentForPlatform(body, platform))
                    .platformTags(adaptTagsForPlatform(tags, platform))
                    .publishingSuggestion(generatePublishingSuggestion(platform))
                    .build();
            
            platformContents.put(platform.name(), platformContent);
        }
        
        return platformContents;
    }

    /**
     * 为平台适配标题
     */
    private String adaptTitleForPlatform(String title, ContentGenerationRequest.Platform platform) {
        switch (platform) {
            case XIAOHONGSHU:
                return "🏠" + title + "✨";
            case XIANYU:
                return "【急售】" + title;
            case ANJUKE:
                return title + " - 专业推荐";
            default:
                return title;
        }
    }

    /**
     * 为平台适配内容
     */
    private String adaptContentForPlatform(String content, ContentGenerationRequest.Platform platform) {
        // 根据不同平台的特点调整内容格式
        switch (platform) {
            case XIAOHONGSHU:
                return content + "\n\n💬 评论区留言获取更多信息";
            case XIANYU:
                return content + "\n\n🔥 支持看房，诚心出售";
            case ANJUKE:
                return content + "\n\n📞 专业顾问为您服务";
            default:
                return content;
        }
    }

    /**
     * 为平台适配标签
     */
    private List<String> adaptTagsForPlatform(List<String> tags, ContentGenerationRequest.Platform platform) {
        List<String> adaptedTags = new ArrayList<>(tags);
        
        switch (platform) {
            case XIAOHONGSHU:
                adaptedTags.add("小红书房产");
                adaptedTags.add("买房日记");
                break;
            case XIANYU:
                adaptedTags.add("二手房");
                adaptedTags.add("急售");
                break;
            case ANJUKE:
                adaptedTags.add("专业推荐");
                adaptedTags.add("品质房源");
                break;
        }
        
        return adaptedTags;
    }

    /**
     * 生成发布建议
     */
    private String generatePublishingSuggestion(ContentGenerationRequest.Platform platform) {
        switch (platform) {
            case XIAOHONGSHU:
                return "建议在晚上8-10点发布，配图要精美，多使用emoji";
            case XIANYU:
                return "建议在工作日上午发布，价格要有吸引力";
            case ANJUKE:
                return "建议在周末发布，内容要专业详细";
            default:
                return "建议选择合适的时间发布";
        }
    }

    /**
     * 评估内容质量
     */
    private ContentGenerationResponse.ContentQuality evaluateContentQuality(String content, ContentGenerationRequest request) {
        // 简单的质量评估逻辑
        double overallScore = 8.5;
        double attractivenessScore = 8.0;
        double professionalismScore = 9.0;
        double readabilityScore = 8.5;
        double completenessScore = 8.8;
        
        List<String> improvements = Arrays.asList(
                "可以增加更多数据支撑",
                "建议添加更多视觉元素",
                "可以强化行动召唤"
        );
        
        return ContentGenerationResponse.ContentQuality.builder()
                .overallScore(overallScore)
                .attractivenessScore(attractivenessScore)
                .professionalismScore(professionalismScore)
                .readabilityScore(readabilityScore)
                .completenessScore(completenessScore)
                .improvementSuggestions(improvements)
                .build();
    }
}
