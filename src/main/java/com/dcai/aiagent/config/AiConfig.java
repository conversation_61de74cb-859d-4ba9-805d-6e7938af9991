package com.dcai.aiagent.config;

import com.dcai.aiagent.function.MarketDataFunction;
import com.dcai.aiagent.function.PriceAnalysisFunction;
import com.dcai.aiagent.function.PropertyDataFunction;
import com.dcai.aiagent.function.SurroundingAmenitiesFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置类
 * 配置ChatClient并显式注册所有Function/Tool
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class AiConfig {

    private final PropertyDataFunction propertyDataFunction;
    private final MarketDataFunction marketDataFunction;
    private final SurroundingAmenitiesFunction surroundingAmenitiesFunction;
    private final PriceAnalysisFunction priceAnalysisFunction;

    /**
     * 配置ChatClient并显式注册所有Function
     * 确保DeepSeek模型能够正确调用Function Calling
     */
    @Bean
    public ChatClient chatClient(DeepSeekChatModel chatModel) {
        log.info("🔧 配置ChatClient，显式注册Function方法...");

        ChatClient client = ChatClient.builder(chatModel)
                // 显式注册所有Function方法
                .defaultFunctions(
                    "getPropertyData",           // PropertyDataFunction.getPropertyData
                    "getMarketData",            // MarketDataFunction.getMarketData
                    "getSurroundingAmenities",  // SurroundingAmenitiesFunction.getSurroundingAmenities
                    "analyzePriceData"          // PriceAnalysisFunction.analyzePriceData
                )
                .build();

        log.info("✅ ChatClient配置完成，已注册4个Function方法");
        return client;
    }
}
