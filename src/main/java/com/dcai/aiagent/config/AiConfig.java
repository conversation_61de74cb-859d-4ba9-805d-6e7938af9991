package com.dcai.aiagent.config;

import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置类
 * 配置ChatClient，Tool自动注册通过@Tool注解实现
 */
@Configuration
@RequiredArgsConstructor
public class AiConfig {

    /**
     * 配置ChatClient
     * Tool会通过@Tool注解自动注册
     */
    @Bean
    public ChatClient chatClient(DeepSeekChatModel chatModel) {
        return ChatClient.builder(chatModel)
                .build();
    }
}
