package com.dcai.aiagent.config;

import com.dcai.aiagent.function.MarketDataFunction;
import com.dcai.aiagent.function.PriceAnalysisFunction;
import com.dcai.aiagent.function.PropertyDataFunction;
import com.dcai.aiagent.function.SurroundingAmenitiesFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ChatClient配置类
 * 
 * SpringAI会自动：
 * 1. 扫描所有@Tool注解的方法
 * 2. 将这些方法的描述信息注入到AI模型的系统提示词中
 * 3. 当AI模型需要调用函数时，自动路由到对应的@Tool方法
 * 4. 将函数返回结果传回给AI模型继续处理
 */
@Slf4j
@Configuration
public class ChatClientConfig {

    /**
     * 配置ChatClient，自动注册所有@Tool方法
     *
     * SpringAI的Function Calling机制：
     * 1. 自动发现：扫描所有@Tool注解的方法
     * 2. 自动注册：将方法信息注册到ChatClient
     * 3. 自动调用：AI模型请求时自动调用对应方法
     * 4. 自动传递：将结果传回AI模型
     */
    @Bean
    public ChatClient chatClient(DeepSeekChatModel chatModel) {

        log.info("🔧 配置ChatClient，自动注册Function Calling方法...");

        // SpringAI 1.0.0会自动发现所有@Tool注解的方法
        // 不需要手动注册，也不需要手动添加函数描述到提示词中！
        ChatClient chatClient = ChatClient.builder(chatModel)
                .build();

        log.info("✅ ChatClient配置完成");
        log.info("📋 SpringAI会自动发现以下@Tool方法：");
        log.info("  - getPropertyData: 获取房源详细信息");
        log.info("  - getMarketData: 获取市场数据");
        log.info("  - getSurroundingAmenities: 获取周边配套信息");
        log.info("  - analyzePriceData: 获取价格分析数据");
        log.info("💡 这些方法会自动注入到AI模型的系统提示词中");

        return chatClient;
    }
    
    /**
     * SpringAI Function Calling工作原理说明：
     * 
     * 1. 【自动发现】SpringAI启动时会扫描所有@Tool注解的方法
     * 
     * 2. 【自动注册】将方法信息（名称、描述、参数）注册到ChatClient
     * 
     * 3. 【自动注入】在发送给AI模型的系统提示词中，SpringAI会自动添加类似内容：
     *    ```
     *    You have access to the following functions:
     *    
     *    getPropertyData(propertyId: string): 获取房源详细信息，包括基础信息、小区详情、交易需求等
     *    - propertyId: 房源ID
     *    
     *    getMarketData(regionName: string, dataType?: string): 获取区域市场数据，包括挂牌信息、成交数据、市场趋势、政策信息等
     *    - regionName: 区域名称，如：浦东新区、徐汇区等
     *    - dataType: 数据类型：listing(挂牌数据)、transaction(成交数据)、trend(市场趋势)、policy(政策信息)、all(全部数据)
     *    
     *    ... 其他函数描述
     *    ```
     * 
     * 4. 【自动调用】当AI模型返回函数调用请求时，SpringAI会：
     *    - 解析函数名和参数
     *    - 调用对应的@Tool方法
     *    - 获取返回结果
     *    - 将结果发送回AI模型
     * 
     * 5. 【自动处理】AI模型基于函数返回的真实数据生成最终响应
     * 
     * 因此，开发者只需要：
     * ✅ 用@Tool注解标记方法
     * ✅ 在ChatClient中注册方法名
     * ❌ 不需要手动在提示词中添加函数描述
     * ❌ 不需要手动处理函数调用逻辑
     */
}
