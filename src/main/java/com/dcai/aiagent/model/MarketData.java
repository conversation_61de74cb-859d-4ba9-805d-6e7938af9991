package com.dcai.aiagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 市场数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketData {
    
    /**
     * 区域名称
     */
    private String regionName;
    
    /**
     * 挂牌房源数据
     */
    private ListingData listingData;
    
    /**
     * 成交房源数据
     */
    private TransactionData transactionData;
    
    /**
     * 市场行情
     */
    private MarketTrend marketTrend;
    
    /**
     * 房产政策
     */
    private PropertyPolicy policy;
    
    /**
     * 金融信贷信息
     */
    private FinancialInfo financialInfo;

    /**
     * 挂牌数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ListingData {
        private Integer totalListings;           // 总挂牌量
        private BigDecimal averagePrice;         // 平均挂牌价
        private String priceRange;               // 价格区间
        private Integer newListingsThisMonth;    // 本月新增挂牌
    }

    /**
     * 成交数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransactionData {
        private List<RecentTransaction> recentTransactions;  // 近期成交
        private BigDecimal averageTransactionPrice;          // 平均成交价
        private Integer transactionVolume;                   // 成交量
        private Integer averageDaysOnMarket;                 // 平均成交周期
    }

    /**
     * 近期成交记录
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecentTransaction {
        private String communityName;      // 小区名称
        private String layout;             // 户型
        private BigDecimal area;           // 面积
        private String floor;              // 楼层
        private String orientation;        // 朝向
        private BigDecimal transactionPrice;  // 成交价
        private LocalDate transactionDate;    // 成交日期
        private Integer daysOnMarket;         // 成交周期
    }

    /**
     * 市场趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketTrend {
        private BigDecimal priceChangeRate;      // 价格变化率
        private String trendDirection;           // 趋势方向
        private Integer inventoryLevel;          // 库存水平
        private BigDecimal demandSupplyRatio;    // 供需比
        private String marketHeat;               // 市场热度
    }

    /**
     * 房产政策
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PropertyPolicy {
        private String purchaseRestriction;      // 限购政策
        private String loanRestriction;          // 限贷政策
        private TaxPolicy taxPolicy;             // 税收政策
        private String housingFundPolicy;        // 公积金政策
        private String talentPolicy;             // 人才购房政策
    }

    /**
     * 税收政策
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaxPolicy {
        private BigDecimal vatRate;              // 增值税率
        private BigDecimal deedTaxRate;          // 契税率
        private BigDecimal personalIncomeTaxRate; // 个人所得税率
        private String taxExemptionCondition;    // 免税条件
    }

    /**
     * 金融信贷信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FinancialInfo {
        private BigDecimal currentLPR;           // 当前LPR
        private BigDecimal firstHomeLoanRate;    // 首套房贷利率
        private BigDecimal secondHomeLoanRate;   // 二套房贷利率
        private BigDecimal firstHomeDownPayment; // 首套房首付比例
        private BigDecimal secondHomeDownPayment; // 二套房首付比例
        private String loanApprovalCycle;        // 贷款审批周期
    }
}
