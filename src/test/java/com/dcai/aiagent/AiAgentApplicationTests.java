package com.dcai.aiagent;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 应用程序集成测试
 */
@SpringBootTest
@TestPropertySource(properties = {
        "spring.ai.deepseek.api-key=test-key"
})
class AiAgentApplicationTests {

    @Test
    void contextLoads() {
        // 测试Spring上下文是否能正常加载
    }
}
