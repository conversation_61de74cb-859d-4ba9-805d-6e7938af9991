package com.dcai.aiagent.integration;

import com.dcai.aiagent.model.ContentGenerationRequest;
import com.dcai.aiagent.model.ContentGenerationResponse;
import com.dcai.aiagent.service.ContentGenerationService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 内容生成集成测试
 * 这个测试会真正调用DeepSeek大模型，并触发Function Calling
 * 
 * 运行前请确保：
 * 1. 设置了正确的DEEPSEEK_API_KEY环境变量
 * 2. 网络连接正常
 */
@SpringBootTest
@TestPropertySource(properties = {
        "spring.ai.deepseek.api-key=${DEEPSEEK_API_KEY:test-key}",
        "logging.level.com.dcai.aiagent=DEBUG",
        "logging.level.org.springframework.ai=DEBUG"
})
class ContentGenerationIntegrationTest {

    @Autowired
    private ContentGenerationService contentGenerationService;

    /**
     * 测试生成星河湾房源推荐内容
     * 这个测试会：
     * 1. 调用DeepSeek大模型
     * 2. 触发Function Calling获取房源数据
     * 3. 触发Function Calling获取市场数据
     * 4. 触发Function Calling获取周边配套
     * 5. 触发Function Calling获取价格分析
     * 6. 生成完整的推文内容
     */
    @Test
    void testGeneratePropertyRecommendationContent() {
        // Given - 构建内容生成请求
        ContentGenerationRequest request = ContentGenerationRequest.builder()
                .propertyId("PROP_001") // 星河湾
                .contentType(ContentGenerationRequest.ContentType.PROPERTY_RECOMMENDATION)
                .targetPlatforms(Arrays.asList(ContentGenerationRequest.Platform.XIAOHONGSHU))
                .targetAudience(ContentGenerationRequest.TargetAudience.UPGRADING_BUYER)
                .contentStyle(ContentGenerationRequest.ContentStyle.PROFESSIONAL)
                .wordCount(600)
                .needCharts(true)
                .chartTypes(Arrays.asList(
                        ContentGenerationRequest.ChartType.PRICE_COMPARISON,
                        ContentGenerationRequest.ChartType.AMENITIES_MAP
                ))
                .specialRequirements("突出满五唯一的税费优势和地段价值，强调业主急售的机会")
                .build();

        // When - 调用内容生成服务
        ContentGenerationResponse response = contentGenerationService.generateContent(request);

        // Then - 验证响应结果
        assertNotNull(response, "响应不能为空");
        assertEquals("PROP_001", response.getPropertyId(), "房源ID应该匹配");
        assertEquals(ContentGenerationResponse.GenerationStatus.SUCCESS, response.getStatus(), "生成状态应该为成功");
        assertNotNull(response.getTaskId(), "任务ID不能为空");
        assertNotNull(response.getGeneratedAt(), "生成时间不能为空");
        assertTrue(response.getProcessingTime() > 0, "处理时间应该大于0");

        // 验证生成的内容
        ContentGenerationResponse.GeneratedContent content = response.getContent();
        assertNotNull(content, "生成的内容不能为空");
        assertNotNull(content.getTitle(), "标题不能为空");
        assertNotNull(content.getBody(), "正文不能为空");
        assertNotNull(content.getTags(), "标签不能为空");
        assertTrue(content.getWordCount() > 0, "字数应该大于0");

        // 验证内容质量
        if (content.getQuality() != null) {
            assertTrue(content.getQuality().getOverallScore() > 0, "总体评分应该大于0");
        }

        // 验证平台适配内容
        if (content.getPlatformContents() != null) {
            assertTrue(content.getPlatformContents().containsKey("XIAOHONGSHU"), "应该包含小红书适配内容");
        }

        // 打印生成的内容用于验证
        System.out.println("=== 生成的推文内容 ===");
        System.out.println("标题: " + content.getTitle());
        System.out.println("字数: " + content.getWordCount());
        System.out.println("标签: " + content.getTags());
        System.out.println("正文:");
        System.out.println(content.getBody());
        System.out.println("=== 内容生成完成 ===");
    }

    /**
     * 测试生成翠湖花园投资分析内容
     */
    @Test
    void testGenerateInvestmentAnalysisContent() {
        // Given
        ContentGenerationRequest request = ContentGenerationRequest.builder()
                .propertyId("PROP_002") // 翠湖花园
                .contentType(ContentGenerationRequest.ContentType.INVESTMENT_ANALYSIS)
                .targetPlatforms(Arrays.asList(ContentGenerationRequest.Platform.ANJUKE))
                .targetAudience(ContentGenerationRequest.TargetAudience.INVESTOR)
                .contentStyle(ContentGenerationRequest.ContentStyle.DETAILED)
                .wordCount(700)
                .needCharts(true)
                .chartTypes(Arrays.asList(
                        ContentGenerationRequest.ChartType.INVESTMENT_RETURN,
                        ContentGenerationRequest.ChartType.MARKET_TREND
                ))
                .specialRequirements("重点分析张江地区的投资潜力和租金回报率")
                .build();

        // When
        ContentGenerationResponse response = contentGenerationService.generateContent(request);

        // Then
        assertNotNull(response);
        assertEquals(ContentGenerationResponse.GenerationStatus.SUCCESS, response.getStatus());
        assertNotNull(response.getContent());
        
        // 打印生成的内容
        ContentGenerationResponse.GeneratedContent content = response.getContent();
        System.out.println("=== 投资分析内容 ===");
        System.out.println("标题: " + content.getTitle());
        System.out.println("字数: " + content.getWordCount());
        System.out.println("正文:");
        System.out.println(content.getBody());
        System.out.println("=== 分析完成 ===");
    }

    /**
     * 测试生成学区房分析内容
     */
    @Test
    void testGenerateSchoolDistrictContent() {
        // Given
        ContentGenerationRequest request = ContentGenerationRequest.builder()
                .propertyId("PROP_001") // 星河湾
                .contentType(ContentGenerationRequest.ContentType.SCHOOL_DISTRICT)
                .targetPlatforms(Arrays.asList(ContentGenerationRequest.Platform.WECHAT))
                .targetAudience(ContentGenerationRequest.TargetAudience.FAMILY_WITH_CHILDREN)
                .contentStyle(ContentGenerationRequest.ContentStyle.FRIENDLY)
                .wordCount(550)
                .needCharts(false)
                .specialRequirements("重点介绍对口学校的教育质量和入学政策")
                .build();

        // When
        ContentGenerationResponse response = contentGenerationService.generateContent(request);

        // Then
        assertNotNull(response);
        assertEquals(ContentGenerationResponse.GenerationStatus.SUCCESS, response.getStatus());
        
        // 打印生成的内容
        ContentGenerationResponse.GeneratedContent content = response.getContent();
        System.out.println("=== 学区房分析内容 ===");
        System.out.println("标题: " + content.getTitle());
        System.out.println("字数: " + content.getWordCount());
        System.out.println("正文:");
        System.out.println(content.getBody());
        System.out.println("=== 学区分析完成 ===");
    }
}
