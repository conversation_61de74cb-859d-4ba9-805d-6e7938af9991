package com.dcai.aiagent.integration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * Function Calling调试测试
 * 用于诊断DeepSeek模型的Function Calling支持情况
 */
@SpringBootTest
@TestPropertySource(properties = {
    "logging.level.com.dcai.aiagent=DEBUG"
})
// 使用application.yml中配置的API密钥
public class FunctionCallingDebugTest {

    @Autowired
    private ChatClient chatClient;

    /**
     * 测试简单的Function Calling
     */
    @Test
    void testSimpleFunctionCalling() {
        System.out.println("=== 测试DeepSeek模型Function Calling支持 ===");
        
        String prompt = """
            请调用getPropertyData函数获取房源ID为PROP_001的详细信息，然后告诉我这个房源的小区名称和价格。
            
            注意：你必须先调用getPropertyData函数，不要编造数据。
            """;
        
        System.out.println("📤 发送提示词: " + prompt);
        
        try {
            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();
            
            String content = response.getResult().getOutput().getText();
            System.out.println("📥 AI响应: " + content);
            
            // 检查是否调用了Function
            if (content.contains("星河湾") && content.contains("830万")) {
                System.out.println("✅ Function Calling成功！AI获取了真实数据");
            } else {
                System.out.println("❌ Function Calling失败！AI没有获取真实数据");
                System.out.println("可能的原因：");
                System.out.println("1. DeepSeek模型不支持Function Calling");
                System.out.println("2. SpringAI与DeepSeek的Function Calling集成有问题");
                System.out.println("3. 需要特殊的提示词格式");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试直接的Function调用提示
     */
    @Test
    void testDirectFunctionCall() {
        System.out.println("=== 测试直接Function调用 ===");
        
        String prompt = """
            调用函数：getPropertyData
            参数：propertyId = "PROP_001"
            
            请执行上述函数调用并返回结果。
            """;
        
        System.out.println("📤 发送提示词: " + prompt);
        
        try {
            String response = chatClient.prompt()
                .user(prompt)
                .call()
                .content();
            
            System.out.println("📥 AI响应: " + response);
            
        } catch (Exception e) {
            System.out.println("❌ 调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试ChatClient的Function配置
     */
    @Test
    void testChatClientConfiguration() {
        System.out.println("=== 检查ChatClient配置 ===");
        
        System.out.println("ChatClient类型: " + chatClient.getClass().getName());
        
        // 尝试获取更多信息
        try {
            String response = chatClient.prompt()
                .user("你好，请告诉我你支持哪些功能？")
                .call()
                .content();
            
            System.out.println("AI响应: " + response);
            
        } catch (Exception e) {
            System.out.println("❌ 基础调用失败: " + e.getMessage());
        }
    }
}
