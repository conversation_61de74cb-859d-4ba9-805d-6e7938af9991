package com.dcai.aiagent.integration;

import com.dcai.aiagent.function.MarketDataFunction;
import com.dcai.aiagent.function.PriceAnalysisFunction;
import com.dcai.aiagent.function.PropertyDataFunction;
import com.dcai.aiagent.function.SurroundingAmenitiesFunction;
import com.dcai.aiagent.model.Property;
import com.dcai.aiagent.model.MarketData;
import com.dcai.aiagent.model.SurroundingAmenities;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Function Calling 演示测试
 * 这个测试直接调用各个Function，演示完整的数据获取流程
 */
@SpringBootTest
@TestPropertySource(properties = {
        "spring.ai.deepseek.api-key=***********************************",
        "logging.level.com.dcai.aiagent=DEBUG"
})
class FunctionCallingDemoTest {

    @Autowired
    private PropertyDataFunction propertyDataFunction;
    
    @Autowired
    private MarketDataFunction marketDataFunction;
    
    @Autowired
    private SurroundingAmenitiesFunction surroundingAmenitiesFunction;
    
    @Autowired
    private PriceAnalysisFunction priceAnalysisFunction;

    /**
     * 演示完整的Function Calling流程
     * 模拟AI模型依次调用各个Function获取数据
     */
    @Test
    void testCompleteDataRetrievalFlow() {
        System.out.println("=== 开始演示Function Calling完整流程 ===");
        System.out.println();
        
        String propertyId = "PROP_001";
        
        // 1. 获取房源数据
        System.out.println("🤖 AI模型: 我需要获取房源详细信息...");
        Property property = propertyDataFunction.getPropertyData(propertyId);
        assertNotNull(property);
        System.out.println("✅ 房源数据获取完成");
        System.out.println();
        
        // 2. 获取市场数据
        System.out.println("🤖 AI模型: 我需要获取市场数据...");
        MarketData marketData = marketDataFunction.getMarketData("徐汇区", "all");
        assertNotNull(marketData);
        System.out.println("✅ 市场数据获取完成");
        System.out.println();
        
        // 3. 获取周边配套
        System.out.println("🤖 AI模型: 我需要获取周边配套信息...");
        SurroundingAmenities amenities = surroundingAmenitiesFunction.getSurroundingAmenities(
                propertyId, "all", 2000);
        assertNotNull(amenities);
        System.out.println("✅ 周边配套数据获取完成");
        System.out.println();
        
        // 4. 进行价格分析
        System.out.println("🤖 AI模型: 我需要进行价格分析...");
        PriceAnalysisFunction.PriceAnalysisResult priceAnalysis = 
                priceAnalysisFunction.analyzePriceData(propertyId, "all", 1000);
        assertNotNull(priceAnalysis);
        System.out.println("✅ 价格分析完成");
        System.out.println();
        
        // 5. 基于获取的数据生成推文内容
        System.out.println("🤖 AI模型: 现在我基于获取的真实数据生成推文...");
        generatePromotionalContent(property, marketData, amenities, priceAnalysis);
        
        System.out.println("=== Function Calling演示完成 ===");
    }

    /**
     * 基于Function Calling获取的真实数据生成推文内容
     */
    private void generatePromotionalContent(Property property, MarketData marketData, 
                                          SurroundingAmenities amenities, 
                                          PriceAnalysisFunction.PriceAnalysisResult priceAnalysis) {
        
        System.out.println("📝 基于真实数据生成的推文内容：");
        System.out.println();
        
        // 生成标题
        String title = String.format("🏠【%s】%s·满五唯一省税30万·业主急售🔥", 
                property.getCommunityName(), 
                property.getLayout().getLayoutDescription());
        
        System.out.println("📌 标题: " + title);
        System.out.println();
        
        // 生成正文
        StringBuilder content = new StringBuilder();
        
        // 房源基本信息
        content.append("📍 房源详情：\n");
        content.append(String.format("🏘️ 小区：%s（%d年建成）\n", 
                property.getCommunityName(), property.getBuildYear()));
        content.append(String.format("🏠 户型：%s，建面%.0f㎡\n", 
                property.getLayout().getLayoutDescription(), property.getArea()));
        content.append(String.format("🧭 朝向：%s\n", property.getOrientation()));
        content.append(String.format("💰 价格：%.0f万（单价%.0f元/㎡）\n", 
                property.getListPrice(), property.getUnitPrice()));
        content.append("\n");
        
        // 房源亮点
        content.append("✨ 房源核心亮点：\n");
        for (String feature : property.getFeatures()) {
            content.append("• " + feature + "\n");
        }
        content.append("• " + property.getSellingPoints() + "\n");
        content.append("\n");
        
        // 价格分析
        content.append("📊 价格优势分析：\n");
        content.append(String.format("• 区域平均价格：%.1f万/㎡\n", 
                priceAnalysis.marketComparison().averagePrice()));
        content.append(String.format("• 本房源单价：%.1f万/㎡\n", 
                property.getUnitPrice().divide(java.math.BigDecimal.valueOf(10000))));
        content.append(String.format("• 价格分位数：%d%%（性价比很高）\n", 
                priceAnalysis.marketComparison().pricePercentile()));
        content.append(String.format("• 预期租金收益率：%.1f%%\n", 
                priceAnalysis.investmentAnalysis().expectedRentalYield()));
        content.append("\n");
        
        // 市场数据
        content.append("📈 市场行情：\n");
        content.append(String.format("• %s挂牌量：%d套\n", 
                marketData.getRegionName(), marketData.getListingData().getTotalListings()));
        content.append(String.format("• 成交量：%d套/月\n", 
                marketData.getTransactionData().getTransactionVolume()));
        content.append(String.format("• 市场趋势：%s（价格变化率%.1f%%）\n", 
                marketData.getMarketTrend().getTrendDirection(),
                marketData.getMarketTrend().getPriceChangeRate()));
        content.append("\n");
        
        // 周边配套
        content.append("🚇 周边配套：\n");
        if (amenities.getEducation() != null) {
            content.append(String.format("🎓 学区：%s、%s（%s）\n", 
                    amenities.getEducation().getPrimarySchool(),
                    amenities.getEducation().getMiddleSchool(),
                    amenities.getEducation().getSchoolRanking()));
        }
        if (amenities.getTransportation() != null && !amenities.getTransportation().getSubwayStations().isEmpty()) {
            var subway = amenities.getTransportation().getSubwayStations().get(0);
            content.append(String.format("🚇 地铁：%s%s，步行%d分钟\n", 
                    subway.getLine(), subway.getName(), subway.getWalkTime()));
        }
        content.append("\n");
        
        // 投资建议
        content.append("💎 投资价值：\n");
        for (String recommendation : priceAnalysis.recommendations()) {
            content.append("• " + recommendation + "\n");
        }
        content.append("\n");
        
        // 行动召唤
        content.append("📞 联系看房：\n");
        content.append("• 专业顾问一对一服务\n");
        content.append("• 支持VR看房和实地带看\n");
        content.append("• 协助贷款和过户手续\n");
        content.append("\n");
        
        // 标签
        content.append("#上海买房攻略 #满五唯一 #").append(property.getCommunityName())
                .append(" #改善型住房 #投资置业");
        
        System.out.println("📄 正文内容：");
        System.out.println(content.toString());
        
        System.out.println("📊 内容统计：");
        System.out.println("• 字数：" + content.toString().replaceAll("\\s", "").length() + "字");
        System.out.println("• 数据来源：4个Function调用获取的真实模拟数据");
        System.out.println("• 内容结构：标题+房源详情+亮点+价格分析+市场行情+配套+投资建议+行动召唤+标签");
    }

    /**
     * 测试单个Function的调用
     */
    @Test
    void testIndividualFunctionCalls() {
        System.out.println("=== 测试各个Function的独立调用 ===");
        
        // 测试房源数据Function
        System.out.println("\n1. 测试PropertyDataFunction:");
        Property property = propertyDataFunction.getPropertyData("PROP_001");
        assertNotNull(property);
        assertEquals("星河湾", property.getCommunityName());
        
        // 测试市场数据Function
        System.out.println("\n2. 测试MarketDataFunction:");
        MarketData marketData = marketDataFunction.getMarketData("徐汇区", "all");
        assertNotNull(marketData);
        assertEquals("徐汇区", marketData.getRegionName());
        
        // 测试周边配套Function
        System.out.println("\n3. 测试SurroundingAmenitiesFunction:");
        SurroundingAmenities amenities = surroundingAmenitiesFunction.getSurroundingAmenities("PROP_001", "all", 2000);
        assertNotNull(amenities);
        assertNotNull(amenities.getEducation());
        
        // 测试价格分析Function
        System.out.println("\n4. 测试PriceAnalysisFunction:");
        PriceAnalysisFunction.PriceAnalysisResult analysis = priceAnalysisFunction.analyzePriceData("PROP_001", "all", 1000);
        assertNotNull(analysis);
        assertEquals("PROP_001", analysis.propertyId());
        
        System.out.println("\n✅ 所有Function调用测试通过！");
    }
}
