package com.dcai.aiagent.function;

import com.dcai.aiagent.model.Property;
import com.dcai.aiagent.service.PropertyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 房源数据Function测试
 */
@ExtendWith(MockitoExtension.class)
class PropertyDataFunctionTest {

    @Mock
    private PropertyService propertyService;

    private PropertyDataFunction propertyDataFunction;

    @BeforeEach
    void setUp() {
        propertyDataFunction = new PropertyDataFunction(propertyService);
    }

    @Test
    void testGetPropertyData_Success() {
        // Given
        String propertyId = "PROP_001";
        Property mockProperty = Property.builder()
                .id(propertyId)
                .communityName("星河湾")
                .listPrice(new BigDecimal("830"))
                .area(new BigDecimal("120"))
                .status(Property.PropertyStatus.AVAILABLE)
                .build();

        when(propertyService.getPropertyById(propertyId)).thenReturn(mockProperty);

        // When
        Property result = propertyDataFunction.getPropertyData(propertyId);

        // Then
        assertNotNull(result);
        assertEquals(propertyId, result.getId());
        assertEquals("星河湾", result.getCommunityName());
        assertEquals(new BigDecimal("830"), result.getListPrice());
    }

    @Test
    void testGetPropertyData_PropertyNotFound() {
        // Given
        String propertyId = "PROP_999";
        when(propertyService.getPropertyById(propertyId)).thenReturn(null);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            propertyDataFunction.getPropertyData(propertyId);
        });

        assertTrue(exception.getMessage().contains("房源不存在"));
    }

    @Test
    void testGetPropertyData_Exception() {
        // Given
        String propertyId = "PROP_001";
        when(propertyService.getPropertyById(anyString()))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            propertyDataFunction.getPropertyData(propertyId);
        });

        assertTrue(exception.getMessage().contains("获取房源数据失败"));
        assertTrue(exception.getMessage().contains("数据库连接失败"));
    }
}
